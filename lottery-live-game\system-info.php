<?php

/**
 * 跨平台系统信息检测脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载环境变量
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
}

echo "========================================\n";
echo "彩票直播游戏系统 - 系统信息\n";
echo "========================================\n\n";

// 操作系统信息
echo "操作系统信息:\n";
echo "- 系统: " . PHP_OS . "\n";
echo "- 架构: " . php_uname('m') . "\n";
echo "- 内核: " . php_uname('r') . "\n";
echo "- 主机名: " . php_uname('n') . "\n";

$isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
$isLinux = strtoupper(substr(PHP_OS, 0, 5)) === 'LINUX';

echo "- 平台类型: " . ($isWindows ? 'Windows' : ($isLinux ? 'Linux' : 'Unix-like')) . "\n\n";

// PHP信息
echo "PHP环境信息:\n";
echo "- PHP版本: " . PHP_VERSION . "\n";
echo "- SAPI: " . PHP_SAPI . "\n";
echo "- 内存限制: " . ini_get('memory_limit') . "\n";
echo "- 最大执行时间: " . ini_get('max_execution_time') . "秒\n";
echo "- 时区: " . date_default_timezone_get() . "\n";

// 检查必要的PHP扩展
echo "\nPHP扩展检查:\n";
$requiredExtensions = [
    'pdo' => 'PDO数据库抽象层',
    'pdo_mysql' => 'MySQL PDO驱动',
    'mysqli' => 'MySQL改进扩展',
    'openssl' => 'OpenSSL加密',
    'curl' => 'cURL网络库',
    'mbstring' => '多字节字符串',
    'json' => 'JSON处理',
    'zip' => 'ZIP压缩',
    'gd' => 'GD图像处理',
    'fileinfo' => '文件信息',
];

foreach ($requiredExtensions as $ext => $desc) {
    $status = extension_loaded($ext) ? '✓' : '✗';
    $color = extension_loaded($ext) ? '' : ' (缺失)';
    echo "- {$status} {$ext}: {$desc}{$color}\n";
}

// Composer信息
echo "\nComposer信息:\n";
if (file_exists(__DIR__ . '/composer.json')) {
    $composer = json_decode(file_get_contents(__DIR__ . '/composer.json'), true);
    echo "- 项目名称: " . ($composer['name'] ?? 'N/A') . "\n";
    echo "- 项目描述: " . ($composer['description'] ?? 'N/A') . "\n";
    
    if (file_exists(__DIR__ . '/vendor/composer/installed.json')) {
        $installed = json_decode(file_get_contents(__DIR__ . '/vendor/composer/installed.json'), true);
        $packages = $installed['packages'] ?? $installed ?? [];
        echo "- 已安装包数量: " . count($packages) . "\n";
    }
} else {
    echo "- Composer配置文件不存在\n";
}

// 数据库连接检查
echo "\n数据库连接检查:\n";
try {
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $database = $_ENV['DB_DATABASE'] ?? 'xin';
    $username = $_ENV['DB_USERNAME'] ?? 'xin';
    $password = $_ENV['DB_PASSWORD'] ?? 'xin00';
    
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "- ✓ 数据库连接正常\n";
    echo "- 数据库: {$database}@{$host}:{$port}\n";
    
    // 检查表
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "- 数据表数量: " . count($tables) . "\n";
    
} catch (Exception $e) {
    echo "- ✗ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 端口检查
echo "\n端口占用检查:\n";
$ports = [8000, 8080, 8081];

foreach ($ports as $port) {
    $connection = @fsockopen('127.0.0.1', $port, $errno, $errstr, 1);
    if ($connection) {
        echo "- ✗ 端口 {$port} 已被占用\n";
        fclose($connection);
    } else {
        echo "- ✓ 端口 {$port} 可用\n";
    }
}

// 文件权限检查
echo "\n文件权限检查:\n";
$directories = [
    'storage/logs' => '日志目录',
    'storage/framework/cache' => '缓存目录',
    'storage/framework/sessions' => '会话目录',
    'storage/framework/views' => '视图缓存目录',
    'bootstrap/cache' => '启动缓存目录',
];

foreach ($directories as $dir => $desc) {
    $fullPath = __DIR__ . '/' . $dir;
    if (!is_dir($fullPath)) {
        @mkdir($fullPath, 0755, true);
    }
    
    if (is_writable($fullPath)) {
        echo "- ✓ {$desc} ({$dir}) 可写\n";
    } else {
        echo "- ✗ {$desc} ({$dir}) 不可写\n";
    }
}

// 性能信息
echo "\n性能信息:\n";
echo "- CPU核心数: " . (function_exists('shell_exec') ? 
    ($isWindows ? 
        trim(shell_exec('echo %NUMBER_OF_PROCESSORS%')) : 
        trim(shell_exec('nproc'))) : 
    'N/A') . "\n";

$memoryUsage = memory_get_usage(true);
$memoryPeak = memory_get_peak_usage(true);
echo "- 当前内存使用: " . formatBytes($memoryUsage) . "\n";
echo "- 峰值内存使用: " . formatBytes($memoryPeak) . "\n";

if (function_exists('sys_getloadavg') && !$isWindows) {
    $load = sys_getloadavg();
    echo "- 系统负载: " . implode(', ', array_map(function($l) { return number_format($l, 2); }, $load)) . "\n";
}

// 推荐配置
echo "\n推荐配置:\n";
echo "- WebSocket进程数: " . ($isWindows ? '1 (Windows限制)' : '4 (多进程)') . "\n";
echo "- 最大连接数: 1000\n";
echo "- 内存限制: 512M或更高\n";
echo "- 执行时间: 无限制 (0)\n";

echo "\n========================================\n";
echo "系统信息检查完成\n";
echo "========================================\n";

// 格式化字节数
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
