<?php

/**
 * 跨平台启动脚本
 * 自动检测操作系统并调用相应的启动脚本
 */

echo "========================================\n";
echo "彩票直播游戏系统 - 跨平台启动器\n";
echo "========================================\n\n";

// 检测操作系统
$isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
$isLinux = strtoupper(substr(PHP_OS, 0, 5)) === 'LINUX';

echo "检测到的操作系统: " . PHP_OS . "\n";
echo "平台类型: " . ($isWindows ? 'Windows' : ($isLinux ? 'Linux' : 'Unix-like')) . "\n\n";

// 显示系统信息
echo "正在检查系统环境...\n";
include __DIR__ . '/system-info.php';

echo "\n========================================\n";
echo "启动选项\n";
echo "========================================\n\n";

if ($isWindows) {
    echo "检测到Windows系统，推荐使用以下方式启动：\n\n";
    echo "方式1 (推荐): 双击运行 start-windows.bat\n";
    echo "方式2: 在命令行中运行: start-windows.bat\n";
    echo "方式3: 手动启动:\n";
    echo "  - 打开命令行窗口1: php artisan serve\n";
    echo "  - 打开命令行窗口2: php bin/websocket-cross-platform.php\n\n";
    
    echo "是否现在启动服务器? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $input = trim(fgets($handle));
    fclose($handle);
    
    if (strtolower($input) === 'y' || strtolower($input) === 'yes') {
        echo "\n正在启动Windows服务器...\n";
        if (file_exists(__DIR__ . '/start-windows.bat')) {
            system('start-windows.bat');
        } else {
            echo "错误: start-windows.bat 文件不存在\n";
        }
    }
    
} else {
    echo "检测到Linux/Unix系统，推荐使用以下方式启动：\n\n";
    echo "方式1 (推荐): ./start-linux.sh\n";
    echo "方式2: bash start-linux.sh\n";
    echo "方式3: 手动启动:\n";
    echo "  - 终端1: php artisan serve\n";
    echo "  - 终端2: php bin/websocket-cross-platform.php\n\n";
    
    // 检查脚本权限
    if (!is_executable(__DIR__ . '/start-linux.sh')) {
        echo "注意: 需要设置执行权限: chmod +x start-linux.sh stop-linux.sh\n\n";
    }
    
    echo "是否现在启动服务器? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $input = trim(fgets($handle));
    fclose($handle);
    
    if (strtolower($input) === 'y' || strtolower($input) === 'yes') {
        echo "\n正在启动Linux服务器...\n";
        
        // 设置执行权限
        if (!is_executable(__DIR__ . '/start-linux.sh')) {
            chmod(__DIR__ . '/start-linux.sh', 0755);
            chmod(__DIR__ . '/stop-linux.sh', 0755);
        }
        
        if (file_exists(__DIR__ . '/start-linux.sh')) {
            system('./start-linux.sh');
        } else {
            echo "错误: start-linux.sh 文件不存在\n";
        }
    }
}

echo "\n========================================\n";
echo "使用说明\n";
echo "========================================\n\n";

echo "访问地址:\n";
echo "- 前台游戏: http://localhost:8000\n";
echo "- 后台管理: http://localhost:8000/admin\n\n";

echo "测试账号:\n";
echo "- 用户: 6117 / 6677\n";
echo "- 管理员: admin / admin123\n\n";

echo "服务器信息:\n";
echo "- Laravel服务器: http://localhost:8000\n";
echo "- WebSocket服务器: ws://localhost:8080\n\n";

if (!$isWindows) {
    echo "管理命令:\n";
    echo "- 停止服务器: ./stop-linux.sh\n";
    echo "- 查看日志: tail -f logs/laravel.log\n";
    echo "- 重启服务器: ./stop-linux.sh && ./start-linux.sh\n\n";
}

echo "配置文件:\n";
echo "- 环境配置: .env\n";
echo "- 后台路径: 修改 .env 中的 ADMIN_PATH\n";
echo "- 数据库配置: .env 中的 DB_* 配置\n\n";

echo "如需帮助，请查看 README.md 或 INSTALL-GUIDE.md\n";
echo "\n启动器执行完成。\n";
