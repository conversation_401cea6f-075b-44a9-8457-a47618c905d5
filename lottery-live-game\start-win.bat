@echo off
echo ========================================
echo Lottery Live Game System - Windows
echo ========================================

echo.
echo [1/4] Checking PHP...
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PHP not found
    echo Please install PHP and add to PATH
    pause
    exit /b 1
) else (
    echo OK: PHP found
)

echo.
echo [2/4] Installing dependencies...
if not exist "vendor" (
    echo Installing composer packages...
    composer install
    if %errorlevel% neq 0 (
        echo ERROR: Composer install failed
        pause
        exit /b 1
    )
) else (
    echo OK: Dependencies installed
)

echo.
echo [3/4] Setting up application...
findstr /c:"APP_KEY=base64:" .env >nul 2>&1
if %errorlevel% neq 0 (
    php artisan key:generate
)

echo.
echo [4/4] Starting servers...

echo.
echo Starting Laravel server (port 8000)...
start "Laravel Server" cmd /k "php artisan serve"

echo.
echo Waiting 3 seconds...
timeout /t 3 /nobreak > nul

echo.
echo Starting WebSocket server (port 8080)...
start "WebSocket Server" cmd /k "php bin/websocket-cross-platform.php"

echo.
echo ========================================
echo All servers started!
echo ========================================
echo.
echo Frontend: http://localhost:8000
echo Admin: http://localhost:8000/admin
echo.
echo Test Account: 6117 / 6677
echo Admin Account: admin / admin123
echo.
pause
