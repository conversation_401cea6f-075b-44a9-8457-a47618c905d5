<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>彩票直播游戏 - 超低延迟体验</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .username {
            color: #fff;
            font-weight: 600;
            font-size: 16px;
        }

        .balance {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(238, 90, 36, 0.3);
        }

        .logout-btn {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.5);
            color: #ff6b6b;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 0, 0, 0.3);
        }

        /* 主要内容区域 - 参考网站布局 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr auto auto;
            height: 100vh;
            width: 100vw;
            padding: 10px;
            margin: 0;
            gap: 10px;
            overflow: hidden;
            box-sizing: border-box;
        }

        /* 直播画面区域 - 左上角 */
        .video-section {
            grid-column: 1;
            grid-row: 1;
            overflow: hidden;
            position: relative;
        }

        /* 游戏信息区域 - 右上角 */
        .game-info-section {
            grid-column: 2;
            grid-row: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* 下注选项区域 - 跨两列，第二行 */
        .betting-options-section {
            grid-column: 1 / 3;
            grid-row: 2;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        /* 刷新按钮区域 - 跨两列，第三行 */
        .refresh-section {
            grid-column: 1 / 3;
            grid-row: 3;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 60px;
        }

        /* 直播画面区域 */
        .video-section {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            overflow: hidden;
        }

        /* 游戏信息区域 */
        .game-info-section {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #333;
            border-radius: 5px;
            padding: 10px;
            color: white;
        }

        .game-status {
            margin-bottom: 20px;
        }

        .version-round {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .countdown {
            font-size: 14px;
            color: #00ff00;
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
        }

        .logout-btn {
            background: #ff4444;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .video-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .video-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .video-title {
            font-size: 16px;
            font-weight: bold;
            color: #00f5ff;
        }

        .video-stats {
            display: flex;
            gap: 10px;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 历史开奖区域 */
        .history-section {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
            margin: 5px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
        }

        /* 个人信息区域 */
        .profile-section {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
            margin: 5px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .profile-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .profile-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .profile-username {
            font-size: 14px;
            font-weight: bold;
            color: white;
        }

        .profile-balance {
            font-size: 12px;
            color: #00f5ff;
        }

        .profile-logout {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
            color: #ff6b6b;
            padding: 8px 15px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .profile-logout:hover {
            background: rgba(255, 107, 107, 0.3);
        }

        .video-canvas {
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 12px;
            border: 2px solid rgba(0, 245, 255, 0.2);
        }

        /* 游戏状态区域 */
        .game-status {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .round-info {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .countdown {
            font-size: 24px;
            font-weight: bold;
            color: #00f5ff;
            text-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }

        /* 右侧历史号码区域 */
        .history-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 16px;
            padding: 20px;
            height: fit-content;
        }

        .history-title {
            font-size: 16px;
            font-weight: bold;
            color: #00f5ff;
            margin-bottom: 15px;
            text-align: center;
        }

        .history-numbers {
            display: grid;
            grid-template-columns: repeat(9, 1fr);
            gap: 5px;
            max-height: 300px;
            overflow-y: auto;
        }

        .history-number {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 245, 255, 0.2);
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            color: #00f5ff;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        /* 右侧区域 - 投注面板 */
        .right-section {
            width: 600px;
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }

        /* 投注区域 */
        .betting-section {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .betting-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 15px;
            text-align: center;
        }

        /* 投注表格容器 */
        .betting-tables {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
            overflow: hidden;
        }

        /* 投注表格 */
        .bet-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 2px;
            margin-bottom: 5px;
        }

        .bet-table td {
            padding: 0;
        }

        .bet-option {
            width: 100%;
            height: 35px;
            background: rgba(50, 50, 50, 0.9);
            border: 1px solid #666;
            color: white;
            border-radius: 3px;
            font-size: 11px;
            font-weight: normal;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            text-align: left;
            padding: 0 6px;
        }

        .bet-option:hover {
            background: rgba(70, 70, 70, 0.9);
        }

        .bet-option.active {
            background: rgba(100, 100, 100, 0.9);
            border-color: #999;
        }

        .bet-label {
            font-size: 11px;
            line-height: 1;
        }

        .bet-multiplier {
            font-size: 11px;
            color: #ccc;
        }

        .bet-amount {
            font-size: 10px;
            color: #fff;
            min-width: 20px;
            text-align: right;
        }

        /* 刷新按钮 */
        .refresh-section {
            text-align: center;
        }

        .refresh-btn {
            background: rgba(80, 80, 80, 0.9);
            border: 1px solid #666;
            color: white;
            padding: 8px 20px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .refresh-btn:hover {
            background: rgba(100, 100, 100, 0.9);
        }

        /* 投注控制区域 */
        .bet-controls-container {
            margin-top: auto;
            padding-top: 10px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 投注控制 */
        .bet-controls {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: 10px;
        }

        .amount-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .amount-label {
            font-size: 14px;
            color: #fff;
        }

        .amount-buttons {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .amount-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
            min-width: 40px;
        }

        .amount-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(102, 126, 234, 0.4);
        }

        .amount-btn.active {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            box-shadow: 0 3px 8px rgba(238, 90, 36, 0.4);
        }

        .confirm-bet {
            width: 100%;
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(86, 171, 47, 0.3);
        }

        .confirm-bet:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(86, 171, 47, 0.4);
        }

        .confirm-bet:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .bet-action-buttons {
            display: flex;
            gap: 10px;
            margin-top: auto;
            padding-top: 10px;
        }

        /* 响应式设计 - 横屏优先 */
        @media (max-width: 1200px) {
            .right-section {
                width: 500px;
            }

            .bet-option {
                height: 45px;
                font-size: 12px;
            }
        }

        /* 强制所有设备使用横屏布局 */
        @media (max-width: 900px) {
            .main-content {
                flex-direction: row !important;
                height: calc(100vh - 60px);
                padding: 8px;
                gap: 8px;
            }

            .header {
                height: 60px;
                padding: 8px 15px;
            }

            .left-section {
                flex: 0 0 45%;
                min-width: 300px;
            }

            .right-section {
                flex: 1;
                width: auto;
                height: 100%;
                overflow: hidden;
            }

            .betting-section {
                height: 100%;
                padding: 10px;
                display: flex;
                flex-direction: column;
            }

            .betting-tables {
                flex: 1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                gap: 3px;
            }

            .bet-table {
                margin-bottom: 3px;
            }

            .bet-option {
                height: 35px;
                font-size: 10px;
            }

            .bet-controls-container {
                margin-top: auto;
                padding-top: 8px;
            }
        }

        /* 小屏幕优化 - 统一横屏布局 */
        @media (max-width: 700px) {
            .main-content {
                flex-direction: row !important;
                padding: 5px;
                gap: 5px;
                height: calc(100vh - 50px);
            }

            .header {
                padding: 5px 10px;
                height: 50px;
            }

            .username, .balance {
                font-size: 11px;
            }

            .left-section {
                flex: 0 0 40%;
                min-width: 250px;
            }

            .video-container {
                padding: 8px;
                min-height: 80px;
            }

            .video-title {
                font-size: 14px;
            }

            .video-stats {
                font-size: 10px;
                gap: 8px;
            }

            .game-info {
                padding: 8px;
            }

            .game-round, .countdown {
                font-size: 12px;
            }

            .history-section {
                padding: 8px;
            }

            .history-title {
                font-size: 12px;
                margin-bottom: 8px;
            }

            .history-numbers {
                gap: 3px;
            }

            .history-number {
                width: 20px;
                height: 20px;
                font-size: 10px;
            }

            .right-section {
                flex: 1;
                height: 100%;
                overflow: hidden;
            }

            .betting-section {
                padding: 8px;
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            .betting-title {
                font-size: 12px;
                margin-bottom: 5px;
            }

            .betting-tables {
                flex: 1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                gap: 2px;
            }

            .bet-table {
                margin-bottom: 2px;
            }

            .bet-option {
                height: 26px;
                font-size: 8px;
            }

            .bet-label {
                font-size: 8px;
                line-height: 1;
            }

            .bet-odds {
                font-size: 7px;
                margin-top: 1px;
            }

            .bet-amount {
                font-size: 7px;
                padding: 1px 2px;
                bottom: 1px;
                right: 2px;
            }

            .bet-controls-container {
                margin-top: auto;
                padding-top: 5px;
                flex-shrink: 0;
            }

            .bet-controls {
                padding: 5px;
            }

            .amount-selector {
                margin-bottom: 5px;
                gap: 3px;
            }

            .amount-label {
                font-size: 10px;
            }

            .amount-buttons {
                gap: 3px;
            }

            .amount-btn {
                padding: 3px 6px;
                font-size: 9px;
                min-width: 30px;
            }

            .confirm-bet {
                padding: 6px;
                font-size: 12px;
            }
        }

        /* 移除多余的媒体查询 - 统一使用上面的横屏布局 */

        /* 移除横屏提示 - 强制横屏布局 */

        /* 新增网格布局样式 */
        .video-placeholder {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bet-type {
            font-size: 16px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .bet-odds {
            font-size: 14px;
            color: #00f5ff;
        }

        .bet-amount {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ff6b6b;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            display: none;
        }

        .bet-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }

        .amount-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 8px;
            padding: 10px;
            color: white;
            width: 120px;
            text-align: center;
        }

        .bet-button {
            background: linear-gradient(135deg, #00f5ff 0%, #0080ff 100%);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 10px 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bet-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 245, 255, 0.4);
        }

        .bet-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .betting-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .betting-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>


    <!-- 移除旧头部，个人信息已整合到网格布局中 -->

    <!-- 主要内容区域 - 参考网站布局 -->
    <div class="main-content">
        <!-- 直播画面区域 - 左上角 -->
        <div class="video-section">
            <iframe src="" style="width: 100%; height: 100%; border: none; background: #000;"></iframe>
        </div>

        <!-- 游戏信息区域 - 右上角 -->
        <div class="game-info-section">
            <!-- 游戏版本和局数 -->
            <div class="game-status">
                <div class="version-round">
                    <span>第 <span id="versionNumber">{{ $currentGame->version_number ?? 0 }}</span> 版</span>
                    <span>第 <span id="roundNumber">{{ $currentGame->round_number ?? 0 }}</span> 局</span>
                </div>
                <div class="countdown">
                    <span>倒计时: <span id="countdown">{{ $currentGame->remaining_time ?? 0 }}</span></span>
                </div>
            </div>

            <!-- 用户信息 -->
            <div class="user-info">
                <span>积分: <span id="userBalance">{{ number_format($balance ?? 11900, 2) }}</span></span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>

        <!-- 下注选项区域 - 跨两列 -->
        <div class="betting-options-section">
            <!-- 第一个表格：基础投注 -->
            <table class="bet-table">
                <tr>
                    <td>
                        <div class="bet-option" data-type="small" data-value="">
                            <span class="bet-label">小(1-12)</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-small"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="zero" data-value="0">
                            <span class="bet-label">0</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-zero"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="big" data-value="">
                            <span class="bet-label">大(13-24)</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-big"></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="bet-option" data-type="odd" data-value="">
                            <span class="bet-label">单</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-odd"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="even" data-value="">
                            <span class="bet-label">双</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-even"></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="bet-option" data-type="range" data-value="1-8">
                            <span class="bet-label">1-8</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-1-8"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="9-16">
                            <span class="bet-label">9-16</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-9-16"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="17-24">
                            <span class="bet-label">17-24</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-17-24"></div>
                        </div>
                    </td>
                </tr>
            </table>

            <!-- 第二个表格：详细投注选项 -->
            <table class="bet-table">
                <tr>
                    <td>
                        <div class="bet-option" data-type="range" data-value="1-6">
                            <span class="bet-label">1-6</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-1-6"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="7-12">
                            <span class="bet-label">7-12</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-7-12"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="13-18">
                            <span class="bet-label">13-18</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-13-18"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="19-24">
                            <span class="bet-label">19-24</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-19-24"></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="bet-option" data-type="range" data-value="1-3">
                            <span class="bet-label">1-3</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-1-3"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="7-9">
                            <span class="bet-label">7-9</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-7-9"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="13-15">
                            <span class="bet-label">13-15</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-13-15"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="19-21">
                            <span class="bet-label">19-21</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-19-21"></div>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <div class="bet-option" data-type="range" data-value="4-6">
                            <span class="bet-label">4-6</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-4-6"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="10-12">
                            <span class="bet-label">10-12</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-10-12"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="16-18">
                            <span class="bet-label">16-18</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-16-18"></div>
                        </div>
                    </td>
                    <td>
                        <div class="bet-option" data-type="range" data-value="22-24">
                            <span class="bet-label">22-24</span>
                            <span class="bet-multiplier">x</span>
                            <div class="bet-amount" id="amount-22-24"></div>
                        </div>
                    </td>
                </tr>

                <!-- 单个数字行 -->
                @for($row = 0; $row < 6; $row++)
                <tr>
                    @for($col = 0; $col < 4; $col++)
                        @php $number = $row * 4 + $col + 1; @endphp
                        @if($number <= 24)
                        <td>
                            <div class="bet-option" data-type="number" data-value="{{ $number }}">
                                <span class="bet-label">{{ $number }}</span>
                                <span class="bet-multiplier">x</span>
                                <div class="bet-amount" id="amount-{{ $number }}"></div>
                            </div>
                        </td>
                        @endif
                    @endfor
                </tr>
                @endfor
            </table>
        </div>

        <!-- 刷新按钮区域 -->
        <div class="refresh-section">
            <button class="refresh-btn" onclick="clearAllBets()">刷新</button>
        </div>
    </div>




    <script>
        // 全局变量
        let ws = null;
        let selectedBetType = null;
        let selectedBetValue = null;
        let selectedAmount = 10;
        let gameData = {
            currentGame: @json($currentGame),
            userBalance: {{ Auth::user()->balance }}
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            initBettingInterface();
            initAmountSelector();
            startGameStatusUpdate();
        });

        // WebSocket连接
        function initWebSocket() {
            ws = new WebSocket('ws://localhost:8080');
            
            ws.onopen = function() {
                console.log('WebSocket连接成功');
                // 加入游戏
                ws.send(JSON.stringify({
                    type: 'join_game',
                    user_id: {{ Auth::user()->id }}
                }));
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            };
            
            ws.onclose = function() {
                console.log('WebSocket连接断开，3秒后重连...');
                setTimeout(initWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            switch(message.type) {
                case 'video_frame':
                    renderVideoFrame(message.data, message.timestamp);
                    break;
                case 'game_status':
                    updateGameStatus(message.data);
                    break;
                case 'lottery_result':
                    handleLotteryResult(message.data);
                    break;
                case 'online_update':
                    updateOnlineUsers(message.data.online_users);
                    break;
            }
        }

        // 视频播放器初始化
        function initVideoPlayer() {
            const canvas = document.getElementById('videoCanvas');
            const ctx = canvas.getContext('2d');
            
            // 设置画布样式
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 显示等待文字
            ctx.fillStyle = '#00f5ff';
            ctx.font = '24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('等待视频流连接...', canvas.width / 2, canvas.height / 2);
        }

        // 渲染视频帧
        function renderVideoFrame(base64Data, serverTimestamp) {
            const canvas = document.getElementById('videoCanvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = function() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                // 计算延迟
                const latency = Date.now() - (serverTimestamp * 1000);
                document.getElementById('latency').textContent = Math.round(latency) + 'ms';
            };
            
            img.src = 'data:image/jpeg;base64,' + base64Data;
        }

        // 投注界面初始化
        function initBettingInterface() {
            // 所有投注选项
            document.querySelectorAll('.bet-option').forEach(option => {
                option.addEventListener('click', function() {
                    selectedBetType = this.dataset.type;
                    selectedBetValue = this.dataset.value;

                    // 移除其他选中状态
                    document.querySelectorAll('.bet-option').forEach(opt => {
                        opt.classList.remove('active');
                    });

                    // 添加选中状态
                    this.classList.add('active');

                    // 添加投注金额到选项
                    addBetAmount(this);
                });
            });
        }

        // 添加投注金额到选项
        function addBetAmount(option) {
            const betType = option.dataset.type;
            const betValue = option.dataset.value;

            // 生成唯一的ID
            let amountId;
            if (betType === 'number') {
                amountId = `amount-${betValue}`;
            } else if (betType === 'range') {
                amountId = `amount-${betValue}`;
            } else {
                amountId = `amount-${betType}`;
            }

            // 获取当前投注金额元素
            const amountElement = document.getElementById(amountId);
            if (amountElement) {
                const currentAmount = parseInt(amountElement.textContent) || 0;
                const newAmount = currentAmount + selectedAmount;

                // 更新显示金额
                amountElement.textContent = newAmount;

                // 显示金额标签
                option.classList.add('has-bet');
            }
        }

        // 金额选择器初始化
        function initAmountSelector() {
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    selectedAmount = parseInt(this.dataset.amount);

                    // 移除其他选中状态
                    document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));

                    // 添加选中状态
                    this.classList.add('active');
                });
            });
        }

        // 下注
        async function placeBet() {
            if (!selectedBetType) {
                alert('请选择投注类型');
                return;
            }

            if (!selectedAmount || selectedAmount < 10) {
                alert('投注金额不能少于10');
                return;
            }

            try {
                const response = await fetch('/api/game/bet', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        bet_type: selectedBetType,
                        bet_value: selectedBetValue,
                        bet_amount: selectedAmount
                    })
                });

                const data = await response.json();

                if (data.code === 200) {
                    // 更新余额
                    document.getElementById('userBalance').textContent = data.data.balance;

                    // 清除选中状态
                    document.querySelectorAll('.bet-option').forEach(opt => {
                        opt.classList.remove('active');
                    });

                    // 重置选择
                    selectedBetType = null;
                    selectedBetValue = null;

                    alert('投注成功！');
                } else {
                    alert(data.msg || '投注失败');
                }
            } catch (error) {
                console.error('下注错误:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 清除所有投注
        function clearAllBets() {
            // 清除所有投注金额显示
            document.querySelectorAll('.bet-amount').forEach(el => {
                el.textContent = '0';
                el.parentElement.classList.remove('has-bet');
            });

            // 清除选中状态
            document.querySelectorAll('.bet-option').forEach(opt => {
                opt.classList.remove('active');
            });

            // 重置选择
            selectedBetType = null;
            selectedBetValue = null;
        }

        // 更新游戏状态
        function updateGameStatus(data) {
            if (data.online_users) {
                document.getElementById('onlineUsers').textContent = data.online_users + '人';
            }
        }

        // 更新在线用户数
        function updateOnlineUsers(count) {
            document.getElementById('onlineUsers').textContent = count + '人';
        }

        // 处理开奖结果
        function handleLotteryResult(data) {
            // 更新历史号码
            const historyContainer = document.getElementById('historyNumbers');
            data.result_numbers.forEach(number => {
                const numberElement = document.createElement('div');
                numberElement.className = 'history-number';
                numberElement.textContent = number;
                historyContainer.insertBefore(numberElement, historyContainer.firstChild);
            });
            
            // 清除投注金额显示
            document.querySelectorAll('.bet-amount').forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
            
            // 移除选中状态
            document.querySelectorAll('.bet-option').forEach(opt => opt.classList.remove('active'));
            selectedBetType = null;
        }

        // 开始游戏状态更新
        function startGameStatusUpdate() {
            setInterval(async function() {
                try {
                    const response = await fetch('/api/game/status');
                    const data = await response.json();
                    
                    if (data.code === 200) {
                        const gameData = data.data;
                        
                        // 更新倒计时
                        document.getElementById('countdown').textContent = gameData.game.remaining_time;
                        
                        // 更新局数
                        document.getElementById('roundNumber').textContent = gameData.game.round_number;
                        document.getElementById('versionNumber').textContent = gameData.game.version_number;
                        
                        // 更新余额
                        document.getElementById('userBalance').textContent = gameData.user.balance;
                    }
                } catch (error) {
                    console.error('获取游戏状态失败:', error);
                }
            }, 1000);
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                });
                
                if (response.ok) {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('退出失败:', error);
            }
        }

        // 心跳保持连接
        setInterval(function() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({type: 'heartbeat'}));
            }
        }, 30000);

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/logout';

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';

                form.appendChild(csrfToken);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
