<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>彩票直播游戏 - 超低延迟体验</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .username {
            color: #fff;
            font-weight: 600;
            font-size: 16px;
        }

        .balance {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(238, 90, 36, 0.3);
        }

        .logout-btn {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.5);
            color: #ff6b6b;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 0, 0, 0.3);
        }

        /* 主要内容区域 - 横屏布局 */
        .main-content {
            display: flex;
            height: calc(100vh - 70px);
            gap: 15px;
            padding: 15px;
        }

        /* 左侧区域 - 视频和历史 */
        .left-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
            min-width: 0;
        }

        /* 视频播放区域 */
        .video-container {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            flex: 1;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .video-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .video-title {
            font-size: 18px;
            font-weight: bold;
            color: #00f5ff;
        }

        .video-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .video-canvas {
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 12px;
            border: 2px solid rgba(0, 245, 255, 0.2);
        }

        /* 游戏状态区域 */
        .game-status {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .round-info {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .countdown {
            font-size: 24px;
            font-weight: bold;
            color: #00f5ff;
            text-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }

        /* 右侧历史号码区域 */
        .history-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 16px;
            padding: 20px;
            height: fit-content;
        }

        .history-title {
            font-size: 16px;
            font-weight: bold;
            color: #00f5ff;
            margin-bottom: 15px;
            text-align: center;
        }

        .history-numbers {
            display: grid;
            grid-template-columns: repeat(9, 1fr);
            gap: 5px;
            max-height: 300px;
            overflow-y: auto;
        }

        .history-number {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 245, 255, 0.2);
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            color: #00f5ff;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        /* 投注区域 */
        .betting-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
        }

        .betting-title {
            font-size: 18px;
            font-weight: bold;
            color: #00f5ff;
            margin-bottom: 20px;
            text-align: center;
        }

        .betting-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .bet-option {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(0, 245, 255, 0.3);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .bet-option:hover {
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            transform: translateY(-2px);
        }

        .bet-option.active {
            background: rgba(0, 245, 255, 0.2);
            border-color: #00f5ff;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.4);
        }

        .bet-type {
            font-size: 16px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .bet-odds {
            font-size: 14px;
            color: #00f5ff;
        }

        .bet-amount {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ff6b6b;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            display: none;
        }

        .bet-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }

        .amount-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 8px;
            padding: 10px;
            color: white;
            width: 120px;
            text-align: center;
        }

        .bet-button {
            background: linear-gradient(135deg, #00f5ff 0%, #0080ff 100%);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 10px 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bet-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 245, 255, 0.4);
        }

        .bet-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .betting-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .betting-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="user-info">
            <span class="username">{{ Auth::user()->username }}</span>
            <span class="balance">积分: <span id="userBalance">{{ Auth::user()->balance }}</span></span>
        </div>
        <button class="logout-btn" onclick="logout()">退出</button>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- 左侧区域 -->
        <div class="left-section">
            <!-- 视频播放区域 -->
            <div class="video-container">
                <div class="video-header">
                    <h2 class="video-title">🎥 实时直播</h2>
                    <div class="video-stats">
                        <span>延迟: <span id="latency">0ms</span></span>
                        <span>帧率: <span id="fps">0fps</span></span>
                        <span>在线: <span id="onlineUsers">{{ $onlineUsers }}人</span></span>
                    </div>
                </div>
                <canvas id="videoCanvas" class="video-canvas" width="1280" height="400"></canvas>
            </div>

            <!-- 游戏状态区域 -->
            <div class="game-status">
                <div class="game-info">
                    <span class="round-info">第 <span id="versionNumber">{{ $currentGame->version_number }}</span> 版</span>
                    <span class="round-info">第 <span id="roundNumber">{{ $currentGame->round_number }}</span> 局</span>
                </div>
                <div class="countdown">
                    倒计时: <span id="countdown">{{ $currentGame->remaining_time }}</span>
                </div>
            </div>

            <!-- 投注区域 -->
            <div class="betting-section">
                <h3 class="betting-title">🎯 投注选项</h3>
                <div class="betting-grid">
                    <div class="bet-option" data-type="small" data-odds="2.0">
                        <div class="bet-type">小 (1-12)</div>
                        <div class="bet-odds">x2.0</div>
                        <div class="bet-amount" id="amount-small"></div>
                    </div>
                    <div class="bet-option" data-type="big" data-odds="2.0">
                        <div class="bet-type">大 (13-24)</div>
                        <div class="bet-odds">x2.0</div>
                        <div class="bet-amount" id="amount-big"></div>
                    </div>
                    <div class="bet-option" data-type="odd" data-odds="2.0">
                        <div class="bet-type">单</div>
                        <div class="bet-odds">x2.0</div>
                        <div class="bet-amount" id="amount-odd"></div>
                    </div>
                    <div class="bet-option" data-type="even" data-odds="2.0">
                        <div class="bet-type">双</div>
                        <div class="bet-odds">x2.0</div>
                        <div class="bet-amount" id="amount-even"></div>
                    </div>
                </div>
                
                <div class="bet-controls">
                    <input type="number" id="betAmount" class="amount-input" placeholder="投注金额" min="10" max="10000" value="100">
                    <button id="betButton" class="bet-button" onclick="placeBet()">确认投注</button>
                </div>
            </div>
        </div>

        <!-- 右侧历史号码区域 -->
        <div class="history-section">
            <h3 class="history-title">📊 历史号码</h3>
            <div class="history-numbers" id="historyNumbers">
                @foreach($historyGames as $game)
                    @if($game->result_numbers)
                        @foreach($game->result_numbers as $number)
                            <div class="history-number">{{ $number }}</div>
                        @endforeach
                    @endif
                @endforeach
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let ws = null;
        let selectedBetType = null;
        let gameData = {
            currentGame: @json($currentGame),
            userBalance: {{ Auth::user()->balance }}
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            initVideoPlayer();
            initBettingInterface();
            startGameStatusUpdate();
        });

        // WebSocket连接
        function initWebSocket() {
            ws = new WebSocket('ws://localhost:8080');
            
            ws.onopen = function() {
                console.log('WebSocket连接成功');
                // 加入游戏
                ws.send(JSON.stringify({
                    type: 'join_game',
                    user_id: {{ Auth::user()->id }}
                }));
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            };
            
            ws.onclose = function() {
                console.log('WebSocket连接断开，3秒后重连...');
                setTimeout(initWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            switch(message.type) {
                case 'video_frame':
                    renderVideoFrame(message.data, message.timestamp);
                    break;
                case 'game_status':
                    updateGameStatus(message.data);
                    break;
                case 'lottery_result':
                    handleLotteryResult(message.data);
                    break;
                case 'online_update':
                    updateOnlineUsers(message.data.online_users);
                    break;
            }
        }

        // 视频播放器初始化
        function initVideoPlayer() {
            const canvas = document.getElementById('videoCanvas');
            const ctx = canvas.getContext('2d');
            
            // 设置画布样式
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 显示等待文字
            ctx.fillStyle = '#00f5ff';
            ctx.font = '24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('等待视频流连接...', canvas.width / 2, canvas.height / 2);
        }

        // 渲染视频帧
        function renderVideoFrame(base64Data, serverTimestamp) {
            const canvas = document.getElementById('videoCanvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = function() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                // 计算延迟
                const latency = Date.now() - (serverTimestamp * 1000);
                document.getElementById('latency').textContent = Math.round(latency) + 'ms';
            };
            
            img.src = 'data:image/jpeg;base64,' + base64Data;
        }

        // 投注界面初始化
        function initBettingInterface() {
            const betOptions = document.querySelectorAll('.bet-option');
            
            betOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除其他选中状态
                    betOptions.forEach(opt => opt.classList.remove('active'));
                    
                    // 添加选中状态
                    this.classList.add('active');
                    selectedBetType = this.dataset.type;
                });
            });
        }

        // 下注
        async function placeBet() {
            if (!selectedBetType) {
                alert('请选择投注类型');
                return;
            }
            
            const betAmount = document.getElementById('betAmount').value;
            if (!betAmount || betAmount < 10) {
                alert('投注金额不能少于10');
                return;
            }
            
            try {
                const response = await fetch('/api/game/bet', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        bet_type: selectedBetType,
                        bet_amount: parseFloat(betAmount)
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    // 更新余额
                    document.getElementById('userBalance').textContent = data.data.balance;
                    
                    // 显示投注金额
                    const amountElement = document.getElementById('amount-' + selectedBetType);
                    amountElement.textContent = betAmount;
                    amountElement.style.display = 'flex';
                    
                    alert('下注成功！');
                } else {
                    alert(data.msg || '下注失败');
                }
            } catch (error) {
                alert('网络错误，请稍后重试');
                console.error('下注错误:', error);
            }
        }

        // 更新游戏状态
        function updateGameStatus(data) {
            if (data.online_users) {
                document.getElementById('onlineUsers').textContent = data.online_users + '人';
            }
        }

        // 更新在线用户数
        function updateOnlineUsers(count) {
            document.getElementById('onlineUsers').textContent = count + '人';
        }

        // 处理开奖结果
        function handleLotteryResult(data) {
            // 更新历史号码
            const historyContainer = document.getElementById('historyNumbers');
            data.result_numbers.forEach(number => {
                const numberElement = document.createElement('div');
                numberElement.className = 'history-number';
                numberElement.textContent = number;
                historyContainer.insertBefore(numberElement, historyContainer.firstChild);
            });
            
            // 清除投注金额显示
            document.querySelectorAll('.bet-amount').forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
            
            // 移除选中状态
            document.querySelectorAll('.bet-option').forEach(opt => opt.classList.remove('active'));
            selectedBetType = null;
        }

        // 开始游戏状态更新
        function startGameStatusUpdate() {
            setInterval(async function() {
                try {
                    const response = await fetch('/api/game/status');
                    const data = await response.json();
                    
                    if (data.code === 200) {
                        const gameData = data.data;
                        
                        // 更新倒计时
                        document.getElementById('countdown').textContent = gameData.game.remaining_time;
                        
                        // 更新局数
                        document.getElementById('roundNumber').textContent = gameData.game.round_number;
                        document.getElementById('versionNumber').textContent = gameData.game.version_number;
                        
                        // 更新余额
                        document.getElementById('userBalance').textContent = gameData.user.balance;
                    }
                } catch (error) {
                    console.error('获取游戏状态失败:', error);
                }
            }, 1000);
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                });
                
                if (response.ok) {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('退出失败:', error);
            }
        }

        // 心跳保持连接
        setInterval(function() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({type: 'heartbeat'}));
            }
        }, 30000);
    </script>
</body>
</html>
