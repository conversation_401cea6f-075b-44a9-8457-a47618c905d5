@echo off
chcp 65001 >nul
echo ========================================
echo 彩票直播游戏系统 - Windows启动脚本
echo ========================================

echo.
echo [1/6] 检查PHP环境...
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ PHP未安装或未添加到PATH
    echo.
    echo 请安装PHP环境：
    echo 1. 下载XAMPP: https://www.apachefriends.org/
    echo 2. 或下载PHP: https://windows.php.net/download/
    echo 3. 将PHP添加到系统PATH环境变量
    pause
    exit /b 1
) else (
    echo ✓ PHP环境正常
)

echo.
echo [2/6] 检查Composer...
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Composer未安装
    echo 请从 https://getcomposer.org/ 下载安装
    pause
    exit /b 1
) else (
    echo ✓ Composer环境正常
)

echo.
echo [3/6] 安装依赖包...
if not exist "vendor" (
    echo 正在安装依赖包...
    composer install
    if %errorlevel% neq 0 (
        echo ✗ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✓ 依赖包已安装
)

echo.
echo [4/6] 生成应用密钥...
findstr /c:"APP_KEY=base64:" .env >nul 2>&1
if %errorlevel% neq 0 (
    php artisan key:generate
    if %errorlevel% neq 0 (
        echo ✗ 应用密钥生成失败
        pause
        exit /b 1
    )
) else (
    echo ✓ 应用密钥已设置
)

echo.
echo [5/6] 检查数据库...
php check-database.php
if %errorlevel% neq 0 (
    echo ✗ 数据库配置有问题
    pause
    exit /b 1
)

echo.
echo 运行数据库迁移...
php artisan migrate --force
if %errorlevel% neq 0 (
    echo ! 数据库迁移可能已存在，继续...
)

echo.
echo 创建测试用户...
php artisan db:seed --class=UserSeeder --force
if %errorlevel% neq 0 (
    echo ! 测试用户可能已存在，继续...
)

echo.
echo [6/6] 启动服务器...

echo.
echo 启动Laravel开发服务器 (端口8000)...
start "Laravel服务器" cmd /k "echo Laravel服务器启动中... && php artisan serve"

echo.
echo 等待3秒...
timeout /t 3 /nobreak > nul

echo.
echo 启动WebSocket服务器 (端口8080)...
start "WebSocket服务器" cmd /k "echo WebSocket服务器启动中... && php bin/websocket-cross-platform.php"

echo.
echo ========================================
echo 所有服务器启动完成！
echo ========================================
echo.
echo 🌐 访问地址:
echo   前台游戏: http://localhost:8000
echo   后台管理: http://localhost:8000/admin
echo.
echo 👤 测试账号:
echo   用户: 6117 / 6677
echo   管理员: admin / admin123
echo.
echo 🔧 服务器信息:
echo   Laravel服务器: http://localhost:8000
echo   WebSocket服务器: ws://localhost:8080
echo.
echo 💡 提示:
echo   - 如需停止服务器，关闭对应的命令行窗口
echo   - 如遇到问题，请检查防火墙设置
echo   - 后台路径可在.env文件中修改ADMIN_PATH
echo.
pause
