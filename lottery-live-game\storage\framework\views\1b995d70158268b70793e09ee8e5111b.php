<?php $__env->startSection('title', '仪表盘'); ?>

<?php $__env->startSection('content'); ?>
<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number"><?php echo e(number_format($stats['total_users'])); ?></div>
        <div class="stat-label">总用户数</div>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo e(number_format($stats['online_users'])); ?></div>
        <div class="stat-label">在线用户</div>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo e(number_format($stats['total_games'])); ?></div>
        <div class="stat-label">总游戏局数</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">¥<?php echo e(number_format($stats['total_bets'], 2)); ?></div>
        <div class="stat-label">总投注金额</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">¥<?php echo e(number_format($stats['total_wins'], 2)); ?></div>
        <div class="stat-label">总中奖金额</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">¥<?php echo e(number_format($stats['today_bets'], 2)); ?></div>
        <div class="stat-label">今日投注</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">¥<?php echo e(number_format($stats['today_wins'], 2)); ?></div>
        <div class="stat-label">今日中奖</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">¥<?php echo e(number_format($stats['total_bets'] - $stats['total_wins'], 2)); ?></div>
        <div class="stat-label">平台盈利</div>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px;">
    <!-- 最近游戏 -->
    <div>
        <h3 style="margin-bottom: 15px; color: #2c3e50;">最近游戏记录</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>游戏ID</th>
                    <th>局数</th>
                    <th>状态</th>
                    <th>投注金额</th>
                    <th>投注人数</th>
                    <th>时间</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $recent_games; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $game): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td>#<?php echo e($game->id); ?></td>
                    <td>第<?php echo e($game->round_number); ?>局</td>
                    <td>
                        <?php if($game->status === 'finished'): ?>
                            <span class="status-badge status-win">已结束</span>
                        <?php elseif($game->status === 'betting'): ?>
                            <span class="status-badge status-pending">投注中</span>
                        <?php else: ?>
                            <span class="status-badge"><?php echo e($game->status); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>¥<?php echo e(number_format($game->total_bets, 2)); ?></td>
                    <td><?php echo e($game->total_players); ?>人</td>
                    <td><?php echo e($game->created_at->format('H:i:s')); ?></td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>

    <!-- 在线用户 -->
    <div>
        <h3 style="margin-bottom: 15px; color: #2c3e50;">在线用户</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>用户名</th>
                    <th>余额</th>
                    <th>IP地址</th>
                    <th>最后活动</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $online_users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($session->user->username); ?></td>
                    <td>¥<?php echo e(number_format($session->user->balance, 2)); ?></td>
                    <td><?php echo e($session->ip_address); ?></td>
                    <td><?php echo e($session->last_activity->diffForHumans()); ?></td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
</div>

<div style="margin-top: 30px;">
    <h3 style="margin-bottom: 15px; color: #2c3e50;">最新注册用户</h3>
    <table class="table">
        <thead>
            <tr>
                <th>用户ID</th>
                <th>用户名</th>
                <th>余额</th>
                <th>状态</th>
                <th>注册时间</th>
                <th>最后登录</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $recent_users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td>#<?php echo e($user->id); ?></td>
                <td><?php echo e($user->username); ?></td>
                <td>¥<?php echo e(number_format($user->balance, 2)); ?></td>
                <td>
                    <?php if($user->status === 'active'): ?>
                        <span class="status-badge status-active">正常</span>
                    <?php elseif($user->status === 'banned'): ?>
                        <span class="status-badge status-banned">禁用</span>
                    <?php else: ?>
                        <span class="status-badge status-pending"><?php echo e($user->status); ?></span>
                    <?php endif; ?>
                </td>
                <td><?php echo e($user->created_at->format('Y-m-d H:i')); ?></td>
                <td><?php echo e($user->last_login_at ? $user->last_login_at->format('Y-m-d H:i') : '从未登录'); ?></td>
                <td>
                    <a href="<?php echo e(url(env('ADMIN_PATH', 'admin') . '/users/' . $user->id)); ?>" class="btn btn-primary">查看</a>
                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
</div>

<script>
    // 自动刷新统计数据
    setInterval(function() {
        // 这里可以添加AJAX请求来更新统计数据
        // 暂时刷新整个页面
        // location.reload();
    }, 30000); // 30秒刷新一次
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\xin\lottery-live-game\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>