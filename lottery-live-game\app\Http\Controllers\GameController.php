<?php

namespace App\Http\Controllers;

use App\Models\Game;
use App\Models\Bet;
use App\Models\UserSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GameController extends Controller
{


    public function index()
    {
        // 更新用户在线状态
        $this->updateUserActivity();

        // 获取当前游戏
        $currentGame = Game::where('status', 'betting')->first();
        if (!$currentGame || !$currentGame->canBet()) {
            // 如果当前游戏已过期，结束它并创建新游戏
            if ($currentGame && !$currentGame->canBet()) {
                $currentGame->update(['status' => 'finished']);
                $currentGame->generateResult();
                $currentGame->calculateWinnings();
            }
            $currentGame = $this->createNewGame();
        }

        // 获取历史开奖记录
        $historyGames = Game::where('status', 'finished')
            ->orderBy('id', 'desc')
            ->limit(50)
            ->get();

        // 获取在线用户数
        $onlineUsers = UserSession::online()->count();

        return view('game.index', compact('currentGame', 'historyGames', 'onlineUsers'));
    }

    public function mobile()
    {
        // 更新用户在线状态
        $this->updateUserActivity();

        // 获取当前游戏
        $currentGame = Game::where('status', 'betting')->first();
        if (!$currentGame || !$currentGame->canBet()) {
            // 如果当前游戏已过期，结束它并创建新游戏
            if ($currentGame && !$currentGame->canBet()) {
                $currentGame->update(['status' => 'finished']);
                $currentGame->generateResult();
                $currentGame->calculateWinnings();
            }
            $currentGame = $this->createNewGame();
        }

        // 获取历史开奖记录
        $historyGames = Game::where('status', 'finished')
            ->orderBy('id', 'desc')
            ->limit(50)
            ->get();

        // 获取在线用户数
        $onlineUsers = UserSession::online()->count();

        return view('game.mobile', compact('currentGame', 'historyGames', 'onlineUsers'));
    }

    public function getStatus()
    {
        $currentGame = Game::where('status', 'betting')->first();
        if (!$currentGame || !$currentGame->canBet()) {
            // 如果当前游戏已过期，结束它并创建新游戏
            if ($currentGame && !$currentGame->canBet()) {
                $currentGame->update(['status' => 'finished']);
                $currentGame->generateResult();
                $currentGame->calculateWinnings();
            }
            $currentGame = $this->createNewGame();
        }

        $onlineUsers = UserSession::online()->count();
        
        // 获取最新的历史记录
        $latestResults = Game::where('status', 'finished')
            ->orderBy('id', 'desc')
            ->limit(27)
            ->pluck('result_numbers')
            ->flatten()
            ->take(27)
            ->values();

        return response()->json([
            'code' => 200,
            'msg' => '获取成功',
            'status' => 200,
            'data' => [
                'game' => [
                    'id' => $currentGame->id,
                    'round_number' => $currentGame->round_number,
                    'version_number' => $currentGame->version_number,
                    'remaining_time' => $currentGame->remaining_time,
                    'status' => $currentGame->status,
                ],
                'user' => [
                    'balance' => Auth::user()->balance,
                ],
                'online_users' => $onlineUsers,
                'latest_results' => $latestResults,
            ]
        ]);
    }

    public function placeBet(Request $request)
    {
        $request->validate([
            'bet_type' => 'required|in:small,big,odd,even,number,range',
            'bet_value' => 'nullable|string',
            'bet_amount' => 'required|numeric|min:10|max:10000',
        ]);

        $user = Auth::user();
        $currentGame = Game::where('status', 'betting')->first();

        if (!$currentGame || !$currentGame->canBet()) {
            return response()->json([
                'code' => 400,
                'msg' => '当前不能下注',
                'status' => 400
            ]);
        }

        if ($user->balance < $request->bet_amount) {
            return response()->json([
                'code' => 400,
                'msg' => '余额不足',
                'status' => 400
            ]);
        }

        // 获取赔率
        $odds = $this->getOdds($request->bet_type, $request->bet_value);
        $potentialWin = $request->bet_amount * $odds;

        DB::transaction(function () use ($user, $currentGame, $request, $odds, $potentialWin) {
            // 扣除余额
            $user->subtractBalance($request->bet_amount, "游戏{$currentGame->id}下注");

            // 创建下注记录
            Bet::create([
                'user_id' => $user->id,
                'game_id' => $currentGame->id,
                'bet_type' => $request->bet_type,
                'bet_value' => $request->bet_value,
                'bet_amount' => $request->bet_amount,
                'odds' => $odds,
                'potential_win' => $potentialWin,
                'status' => 'pending',
            ]);

            // 更新游戏统计
            $currentGame->increment('total_bets', $request->bet_amount);
            $currentGame->increment('total_players');
        });

        return response()->json([
            'code' => 200,
            'msg' => '下注成功',
            'status' => 200,
            'data' => [
                'balance' => $user->fresh()->balance,
                'potential_win' => $potentialWin,
            ]
        ]);
    }

    private function updateUserActivity()
    {
        $sessionId = session('user_session_id');
        if ($sessionId) {
            UserSession::where('session_id', $sessionId)->first()?->updateActivity();
        }
    }

    private function createNewGame()
    {
        $lastGame = Game::orderBy('id', 'desc')->first();
        $roundNumber = $lastGame ? $lastGame->round_number + 1 : 1;
        $versionNumber = 3; // 固定版本号

        return Game::create([
            'round_number' => $roundNumber,
            'version_number' => $versionNumber,
            'start_time' => now(),
            'end_time' => now()->addSeconds(60), // 60秒下注时间
            'status' => 'betting',
        ]);
    }

    private function getOdds($betType, $betValue = null)
    {
        $odds = [
            'small' => 2.0,   // 小 1-12
            'big' => 2.0,     // 大 13-24
            'odd' => 2.0,     // 单
            'even' => 2.0,    // 双
            'number' => 24.0, // 特定数字
            'range' => 3.0,   // 范围
        ];

        return $odds[$betType] ?? 1.0;
    }
}
