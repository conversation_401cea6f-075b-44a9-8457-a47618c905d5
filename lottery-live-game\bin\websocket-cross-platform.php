<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Workerman\Worker;
use Workerman\Connection\TcpConnection;

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 检测操作系统
$isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';

echo "========================================\n";
echo "彩票直播游戏 - WebSocket服务器\n";
echo "========================================\n";
echo "操作系统: " . PHP_OS . ($isWindows ? " (Windows)" : " (Linux/Unix)") . "\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "启动时间: " . date('Y-m-d H:i:s') . "\n";
echo "========================================\n\n";

// WebSocket服务器配置
$ws_worker = new Worker("websocket://0.0.0.0:8080");
$ws_worker->count = $isWindows ? 1 : 4; // Windows单进程，Linux多进程
$ws_worker->name = 'LotteryWebSocket';

// 存储所有连接
global $connections;
$connections = [];

// 客户端连接时
$ws_worker->onConnect = function(TcpConnection $connection) {
    global $connections;
    $connections[$connection->id] = $connection;
    
    echo "[" . date('Y-m-d H:i:s') . "] 新用户连接: {$connection->id} (总连接数: " . count($connections) . ")\n";
    
    // 发送欢迎消息
    $connection->send(json_encode([
        'type' => 'welcome',
        'data' => [
            'connection_id' => $connection->id,
            'server_time' => time(),
            'online_users' => count($connections),
            'server_info' => [
                'os' => PHP_OS,
                'php_version' => PHP_VERSION,
                'workerman_version' => Worker::VERSION
            ]
        ]
    ]));
};

// 收到客户端消息时
$ws_worker->onMessage = function(TcpConnection $connection, $data) {
    global $connections;
    
    $message = json_decode($data, true);
    if (!$message) {
        return;
    }
    
    switch ($message['type']) {
        case 'heartbeat':
            // 心跳响应
            $connection->send(json_encode([
                'type' => 'pong',
                'timestamp' => microtime(true)
            ]));
            break;
            
        case 'join_game':
            // 用户加入游戏
            $connection->user_id = $message['user_id'] ?? null;
            echo "[" . date('Y-m-d H:i:s') . "] 用户 {$connection->user_id} 加入游戏\n";
            
            // 发送当前游戏状态
            $connection->send(json_encode([
                'type' => 'game_status',
                'data' => [
                    'round_number' => rand(1000, 9999),
                    'remaining_time' => rand(10, 30),
                    'online_users' => count($connections)
                ]
            ]));
            break;
            
        case 'bet':
            // 广播新的投注信息
            broadcastToAll([
                'type' => 'new_bet',
                'data' => [
                    'user_id' => $connection->user_id,
                    'bet_type' => $message['bet_type'] ?? '',
                    'bet_amount' => $message['bet_amount'] ?? 0,
                    'timestamp' => time()
                ]
            ], $connections);
            break;
    }
};

// 客户端断开连接时
$ws_worker->onClose = function(TcpConnection $connection) {
    global $connections;
    unset($connections[$connection->id]);
    
    echo "[" . date('Y-m-d H:i:s') . "] 用户断开: {$connection->id} (剩余连接数: " . count($connections) . ")\n";
    
    // 广播在线人数更新
    broadcastToAll([
        'type' => 'online_update',
        'data' => ['online_users' => count($connections)]
    ], $connections);
};

// 广播消息到所有连接
function broadcastToAll($message, $connections, $encode = true) {
    if (empty($connections)) {
        return;
    }
    
    $data = $encode ? json_encode($message) : $message;
    
    foreach ($connections as $conn) {
        try {
            $conn->send($data);
        } catch (Exception $e) {
            echo "[ERROR] 发送消息失败: " . $e->getMessage() . "\n";
        }
    }
}

// 定时任务 - 游戏状态更新
$ws_worker->onWorkerStart = function() use (&$connections) {
    global $connections;
    
    // 每5秒发送游戏状态更新
    \Workerman\Lib\Timer::add(5, function() use (&$connections) {
        if (empty($connections)) {
            return;
        }
        
        $gameStatus = [
            'type' => 'game_status',
            'data' => [
                'server_time' => time(),
                'online_users' => count($connections),
                'round_number' => rand(1000, 9999),
                'remaining_time' => rand(5, 30)
            ]
        ];
        
        broadcastToAll($gameStatus, $connections);
    });
    
    // 每秒发送模拟视频帧
    \Workerman\Lib\Timer::add(1, function() use (&$connections) {
        if (empty($connections)) {
            return;
        }
        
        // 创建模拟视频帧
        $frameData = createSimulatedFrame();
        
        $message = [
            'type' => 'video_frame',
            'data' => $frameData,
            'timestamp' => microtime(true)
        ];
        
        broadcastToAll($message, $connections);
    });
    
    echo "[" . date('Y-m-d H:i:s') . "] 定时器启动成功\n";
};

// 创建模拟视频帧
function createSimulatedFrame() {
    // 创建简单的文本帧数据
    $frameInfo = [
        'time' => date('Y-m-d H:i:s'),
        'frame_id' => rand(1000, 9999),
        'content' => '彩票直播游戏 - 模拟视频流'
    ];
    
    return base64_encode(json_encode($frameInfo));
}

echo "WebSocket服务器启动中...\n";
echo "监听地址: ws://0.0.0.0:8080\n";
echo "进程数量: " . $ws_worker->count . "\n";
echo "按 Ctrl+C 停止服务器\n\n";

// 启动Worker
Worker::runAll();
