#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}========================================"
echo -e "彩票直播游戏系统 - Linux启动脚本"
echo -e "========================================${NC}"

# 检查PHP环境
echo -e "\n${YELLOW}[1/6] 检查PHP环境...${NC}"
if command -v php &> /dev/null; then
    PHP_VERSION=$(php -v | head -n 1)
    echo -e "${GREEN}✓ PHP环境正常${NC}"
    echo -e "${BLUE}  $PHP_VERSION${NC}"
    
    # 检查PHP版本
    PHP_VER=$(php -r "echo PHP_VERSION_ID;")
    if [ "$PHP_VER" -lt 80200 ]; then
        echo -e "${RED}✗ PHP版本过低，需要8.2或更高版本${NC}"
        exit 1
    fi
else
    echo -e "${RED}✗ PHP未安装${NC}"
    echo -e "${YELLOW}请安装PHP 8.2+:${NC}"
    echo -e "  Ubuntu/Debian: sudo apt install php8.2 php8.2-cli php8.2-mysql php8.2-curl php8.2-mbstring"
    echo -e "  CentOS/RHEL: sudo yum install php php-cli php-mysql php-curl php-mbstring"
    exit 1
fi

# 检查Composer
echo -e "\n${YELLOW}[2/6] 检查Composer...${NC}"
if command -v composer &> /dev/null; then
    COMPOSER_VERSION=$(composer --version)
    echo -e "${GREEN}✓ Composer环境正常${NC}"
    echo -e "${BLUE}  $COMPOSER_VERSION${NC}"
else
    echo -e "${RED}✗ Composer未安装${NC}"
    echo -e "${YELLOW}请安装Composer:${NC}"
    echo -e "  curl -sS https://getcomposer.org/installer | php"
    echo -e "  sudo mv composer.phar /usr/local/bin/composer"
    exit 1
fi

# 安装依赖包
echo -e "\n${YELLOW}[3/6] 安装依赖包...${NC}"
if [ ! -d "vendor" ]; then
    echo -e "${BLUE}正在安装依赖包...${NC}"
    composer install
    if [ $? -ne 0 ]; then
        echo -e "${RED}✗ 依赖安装失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✓ 依赖包已安装${NC}"
fi

# 生成应用密钥
echo -e "\n${YELLOW}[4/6] 生成应用密钥...${NC}"
if ! grep -q "APP_KEY=base64:" .env 2>/dev/null; then
    php artisan key:generate
    if [ $? -ne 0 ]; then
        echo -e "${RED}✗ 应用密钥生成失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✓ 应用密钥已设置${NC}"
fi

# 检查数据库
echo -e "\n${YELLOW}[5/6] 检查数据库...${NC}"
php check-database.php
if [ $? -ne 0 ]; then
    echo -e "${RED}✗ 数据库配置有问题${NC}"
    exit 1
fi

echo -e "\n${BLUE}运行数据库迁移...${NC}"
php artisan migrate --force
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}! 数据库迁移可能已存在，继续...${NC}"
fi

echo -e "\n${BLUE}创建测试用户...${NC}"
php artisan db:seed --class=UserSeeder --force 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}! 测试用户可能已存在，继续...${NC}"
fi

# 启动服务器
echo -e "\n${YELLOW}[6/6] 启动服务器...${NC}"

# 检查端口占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${RED}✗ 端口 $1 已被占用${NC}"
        echo -e "${YELLOW}请关闭占用端口的程序或修改配置${NC}"
        return 1
    fi
    return 0
}

echo -e "\n${BLUE}检查端口占用...${NC}"
check_port 8000 || exit 1
check_port 8080 || exit 1

# 创建日志目录
mkdir -p logs

echo -e "\n${BLUE}启动Laravel开发服务器 (端口8000)...${NC}"
nohup php artisan serve > logs/laravel.log 2>&1 &
LARAVEL_PID=$!
echo $LARAVEL_PID > logs/laravel.pid

echo -e "\n${BLUE}等待3秒...${NC}"
sleep 3

echo -e "\n${BLUE}启动WebSocket服务器 (端口8080)...${NC}"
nohup php bin/websocket-cross-platform.php > logs/websocket.log 2>&1 &
WEBSOCKET_PID=$!
echo $WEBSOCKET_PID > logs/websocket.pid

echo -e "\n${BLUE}等待3秒检查服务器状态...${NC}"
sleep 3

# 检查服务器是否启动成功
if kill -0 $LARAVEL_PID 2>/dev/null; then
    echo -e "${GREEN}✓ Laravel服务器启动成功 (PID: $LARAVEL_PID)${NC}"
else
    echo -e "${RED}✗ Laravel服务器启动失败${NC}"
    cat logs/laravel.log
    exit 1
fi

if kill -0 $WEBSOCKET_PID 2>/dev/null; then
    echo -e "${GREEN}✓ WebSocket服务器启动成功 (PID: $WEBSOCKET_PID)${NC}"
else
    echo -e "${RED}✗ WebSocket服务器启动失败${NC}"
    cat logs/websocket.log
    exit 1
fi

echo -e "\n${CYAN}========================================"
echo -e "所有服务器启动完成！"
echo -e "========================================${NC}"

echo -e "\n${CYAN}🌐 访问地址:${NC}"
echo -e "  前台游戏: ${BLUE}http://localhost:8000${NC}"
echo -e "  后台管理: ${BLUE}http://localhost:8000/admin${NC}"

echo -e "\n${CYAN}👤 测试账号:${NC}"
echo -e "  用户: ${GREEN}6117 / 6677${NC}"
echo -e "  管理员: ${GREEN}admin / admin123${NC}"

echo -e "\n${CYAN}🔧 服务器信息:${NC}"
echo -e "  Laravel服务器: ${BLUE}http://localhost:8000${NC} (PID: $LARAVEL_PID)"
echo -e "  WebSocket服务器: ${BLUE}ws://localhost:8080${NC} (PID: $WEBSOCKET_PID)"

echo -e "\n${CYAN}💡 管理命令:${NC}"
echo -e "  停止服务器: ${YELLOW}./stop-linux.sh${NC}"
echo -e "  查看日志: ${YELLOW}tail -f logs/laravel.log${NC} 或 ${YELLOW}tail -f logs/websocket.log${NC}"
echo -e "  重启服务器: ${YELLOW}./stop-linux.sh && ./start-linux.sh${NC}"

echo -e "\n${GREEN}系统已成功启动，可以开始使用！${NC}"
