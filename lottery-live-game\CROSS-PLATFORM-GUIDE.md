# 🌍 跨平台部署指南

## 📋 系统支持

本彩票直播游戏系统完全支持以下操作系统：

- ✅ **Windows 10/11** (x64)
- ✅ **Linux** (Ubuntu, CentOS, Debian, RHEL)
- ✅ **macOS** (Intel/Apple Silicon)
- ✅ **Unix-like** 系统

## 🚀 快速启动

### 🖥️ Windows 系统

#### 方法一：一键启动 (推荐)
```bash
# 双击运行或在命令行执行
.\start-win.bat
```

#### 方法二：PowerShell启动
```powershell
# 运行跨平台启动器
php start.php
```

#### 方法三：手动启动
```cmd
# 终端1 - Laravel服务器
php artisan serve

# 终端2 - WebSocket服务器  
php bin/websocket-cross-platform.php
```

### 🐧 Linux 系统

#### 方法一：脚本启动 (推荐)
```bash
# 设置执行权限
chmod +x start-linux.sh stop-linux.sh

# 启动服务器
./start-linux.sh

# 停止服务器
./stop-linux.sh
```

#### 方法二：通用启动器
```bash
# 运行跨平台启动器
php start.php
```

#### 方法三：手动启动
```bash
# 后台启动Laravel服务器
nohup php artisan serve > logs/laravel.log 2>&1 &

# 后台启动WebSocket服务器
nohup php bin/websocket-cross-platform.php > logs/websocket.log 2>&1 &
```

### 🍎 macOS 系统

macOS使用与Linux相同的启动方式：

```bash
# 使用Linux脚本
./start-linux.sh

# 或使用通用启动器
php start.php
```

## 🔧 环境配置

### Windows 环境配置

1. **安装PHP 8.2+**
   ```
   选项1: 下载XAMPP (推荐新手)
   - https://www.apachefriends.org/
   
   选项2: 手动安装PHP
   - https://windows.php.net/download/
   - 解压到 C:\php
   - 添加到系统PATH
   ```

2. **安装Composer**
   ```
   下载: https://getcomposer.org/download/
   运行: Composer-Setup.exe
   ```

3. **安装MySQL**
   ```
   选项1: XAMPP自带MySQL
   选项2: MySQL Community Server
   - https://dev.mysql.com/downloads/mysql/
   ```

### Linux 环境配置

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装PHP 8.2
sudo apt install php8.2 php8.2-cli php8.2-mysql php8.2-curl php8.2-mbstring php8.2-zip php8.2-gd

# 安装Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 安装MySQL
sudo apt install mysql-server
```

#### CentOS/RHEL
```bash
# 安装EPEL和Remi仓库
sudo yum install epel-release
sudo yum install https://rpms.remirepo.net/enterprise/remi-release-8.rpm

# 安装PHP 8.2
sudo yum module enable php:remi-8.2
sudo yum install php php-cli php-mysql php-curl php-mbstring php-zip php-gd

# 安装Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 安装MySQL
sudo yum install mysql-server
```

## 🗄️ 数据库配置

### 创建数据库和用户

#### MySQL命令行
```sql
-- 创建数据库
CREATE DATABASE xin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'xin'@'localhost' IDENTIFIED BY 'xin00';

-- 授权
GRANT ALL PRIVILEGES ON xin.* TO 'xin'@'localhost';
FLUSH PRIVILEGES;
```

#### Windows XAMPP
1. 启动XAMPP控制面板
2. 启动Apache和MySQL
3. 访问 http://localhost/phpmyadmin
4. 创建数据库 `xin`
5. 创建用户 `xin` 密码 `xin00`

## 🌐 网络配置

### 防火墙设置

#### Windows防火墙
```cmd
# 允许端口8000和8080
netsh advfirewall firewall add rule name="Laravel Server" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="WebSocket Server" dir=in action=allow protocol=TCP localport=8080
```

#### Linux防火墙 (UFW)
```bash
# 允许端口
sudo ufw allow 8000
sudo ufw allow 8080

# 启用防火墙
sudo ufw enable
```

#### Linux防火墙 (firewalld)
```bash
# 允许端口
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

## 📊 性能优化

### Windows优化
```ini
; php.ini 配置
memory_limit = 512M
max_execution_time = 0
opcache.enable = 1
opcache.memory_consumption = 128
```

### Linux优化
```bash
# 系统优化
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'fs.file-max = 100000' >> /etc/sysctl.conf
sysctl -p

# PHP优化 (同Windows)
```

## 🔍 故障排除

### 常见问题

#### 1. PHP命令不识别
**Windows:**
- 确保PHP路径在系统PATH中
- 重启命令行窗口

**Linux:**
- 检查PHP是否安装: `which php`
- 安装PHP: 参考上面的安装命令

#### 2. 端口被占用
```bash
# 查看端口占用
Windows: netstat -an | findstr ":8000"
Linux: lsof -i :8000

# 杀死进程
Windows: taskkill /PID <PID> /F
Linux: kill -9 <PID>
```

#### 3. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库配置 (.env文件)
- 测试连接: `php check-database.php`

#### 4. WebSocket连接失败
- 检查防火墙设置
- 确认端口8080未被占用
- 查看WebSocket日志

## 📁 文件权限

### Linux权限设置
```bash
# 设置目录权限
chmod -R 755 storage bootstrap/cache
chmod -R 777 storage/logs storage/framework

# 设置脚本权限
chmod +x start-linux.sh stop-linux.sh
```

### Windows权限
Windows通常不需要特殊权限设置，但确保：
- 用户对项目目录有完全控制权限
- 防病毒软件不阻止PHP执行

## 🚀 生产环境部署

### Linux生产环境
```bash
# 使用Nginx + PHP-FPM
sudo apt install nginx php8.2-fpm

# 配置Nginx虚拟主机
# 使用Supervisor管理WebSocket进程
sudo apt install supervisor
```

### Windows生产环境
```
# 使用IIS + PHP
# 或Apache + PHP
# 配置Windows服务运行WebSocket
```

## 📞 技术支持

如遇到问题：

1. **查看日志**
   - Windows: 检查命令行输出
   - Linux: `tail -f logs/*.log`

2. **系统检查**
   ```bash
   php system-info.php
   ```

3. **重启服务**
   - Windows: 关闭命令行窗口重新运行
   - Linux: `./stop-linux.sh && ./start-linux.sh`

---

**注意**: 本系统在不同平台上的性能表现：
- **Windows**: 单进程模式，适合开发和小规模部署
- **Linux**: 多进程模式，适合生产环境和大规模部署
- **macOS**: 与Linux相似，适合开发环境
