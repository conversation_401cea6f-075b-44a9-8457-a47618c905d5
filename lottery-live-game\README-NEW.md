# 🎯 彩票直播游戏系统

## 📖 项目简介

这是一个基于PHP Laravel框架开发的**超低延迟彩票直播游戏系统**，采用WebSocket+Canvas技术实现50-200ms的极低延迟视频直播，支持摄像头设备对接和实时游戏互动。

## ✨ 主要特性

### 🚀 超低延迟直播
- **WebSocket + Canvas** 架构，延迟仅50-200ms
- 支持RTSP摄像头设备对接
- 实时视频流处理和推送
- 自动降级到模拟视频流

### 🎮 完整游戏系统
- 实时彩票游戏逻辑
- 多种投注类型（大小、单双、数字等）
- 自动开奖和结算
- 历史记录查询

### 👥 用户管理
- 用户注册登录系统
- 余额管理和充值
- 在线状态监控
- 投注记录追踪

### 🎨 现代化UI设计
- **Glassmorphism** 毛玻璃效果
- 霓虹灯边框和发光效果
- 响应式设计
- 实时数据更新

## 🛠️ 技术栈

### 后端
- **PHP 8.2+** + **Laravel 11**
- **Workerman** - 高性能WebSocket服务器
- **FFmpeg** - 视频流处理
- **MySQL** - 主数据库
- **SQLite** - 默认开发数据库

### 前端
- **原生JavaScript** + **Canvas API**
- **WebSocket** 实时通信
- **CSS3** 现代化样式
- **响应式设计**

### 直播技术
- **RTSP** 摄像头协议支持
- **WebSocket** 视频帧传输
- **Canvas** 实时渲染
- **FFmpeg** 视频转码

## 🚀 快速开始

### 环境要求
- **PHP 8.2+** (必需)
- **Composer** (必需)
- **MySQL 8.0+** (推荐) 或 SQLite
- **FFmpeg** (可选，用于真实摄像头)

### 🖥️ 跨平台支持
本系统完全支持 **Windows** 和 **Linux** 操作系统。

### 安装步骤

#### 方法一：自动启动器 (推荐)
```bash
# 1. 进入项目目录
cd lottery-live-game

# 2. 运行跨平台启动器
php start.php
```

#### 方法二：平台特定启动

**Windows 用户:**
```bash
# 双击运行或命令行执行
start-windows.bat
```

**Linux 用户:**
```bash
# 设置执行权限
chmod +x start-linux.sh stop-linux.sh

# 启动服务器
./start-linux.sh

# 停止服务器
./stop-linux.sh
```

#### 方法三：手动安装
```bash
# 1. 安装依赖
composer install

# 2. 配置环境
cp .env.example .env
php artisan key:generate

# 3. 配置数据库 (编辑 .env 文件)
# DB_DATABASE=xin
# DB_USERNAME=xin
# DB_PASSWORD=xin00

# 4. 运行迁移
php artisan migrate
php artisan db:seed

# 5. 启动服务器
php artisan serve                    # Laravel服务器 (端口8000)
php bin/websocket-cross-platform.php # WebSocket服务器 (端口8080)
```

### 🌐 访问系统
- **前台游戏**: http://localhost:8000
- **后台管理**: http://localhost:8000/admin
- **测试账号**: `6117` / `6677`
- **管理员**: `admin` / `admin123`

## 📋 测试账号

| 用户名 | 密码 | 余额 | 说明 |
|--------|------|------|------|
| 6117 | 6677 | 10,000 | 主测试账号 |
| admin | admin123 | 50,000 | 管理员账号 |
| test1 | 123456 | 5,000 | 测试账号1 |
| test2 | 123456 | 3,000 | 测试账号2 |

## 🎥 摄像头配置

### 支持的摄像头品牌
- 海康威视 (Hikvision)
- 大华 (Dahua)
- 宇视 (Uniview)
- 其他支持RTSP协议的IP摄像头

### 配置方法

1. **修改 `.env` 文件**
```env
CAMERA_HOST=*************
CAMERA_USERNAME=admin
CAMERA_PASSWORD=admin123
CAMERA_PORT=554
CAMERA_PATH=/stream1
```

2. **网络配置**
- 确保服务器和摄像头在同一网段
- 或配置端口映射/VPN隧道

3. **测试连接**
```bash
# 测试RTSP连接
ffprobe -v quiet -print_format json -show_streams "rtsp://admin:password@*************:554/stream1"
```

## 🔧 系统架构

```
摄像头 → RTSP流 → FFmpeg解码 → 帧数据 → WebSocket服务器 → 前端Canvas渲染
   ↓
PHP后台管理系统 ← → SQLite/MySQL数据库 ← → 游戏逻辑服务器
```

### 服务器端口
- **8000** - Laravel Web服务器
- **8080** - WebSocket服务器
- **8081** - HTTP API服务器

## 📊 性能指标

| 指标 | 数值 |
|------|------|
| 视频延迟 | 50-200ms |
| 并发用户 | 500-1500人 |
| 帧率 | 30fps |
| 分辨率 | 1280x720 |
| 内存占用 | < 512MB |

## 🎯 游戏规则

### 投注类型
- **小 (1-12)** - 赔率 2.0倍
- **大 (13-24)** - 赔率 2.0倍  
- **单数** - 赔率 2.0倍
- **双数** - 赔率 2.0倍
- **特定数字** - 赔率 24.0倍

### 游戏流程
1. 每局游戏30秒投注时间
2. 自动生成27个随机数字(0-24)
3. 根据结果自动结算
4. 立即开始下一局

## 🔒 安全特性

- CSRF保护
- SQL注入防护
- XSS过滤
- 用户会话管理
- 余额变动日志

## 📱 移动端支持

- 响应式设计，支持手机和平板
- Touch友好的交互界面
- 自适应视频播放器

## 🛠️ 开发指南

### 添加新的投注类型
1. 修改 `Game.php` 模型的 `checkBetWin` 方法
2. 更新前端投注界面
3. 调整赔率配置

### 自定义UI主题
1. 修改 CSS 变量
2. 调整颜色方案
3. 更新动画效果

### 扩展WebSocket功能
1. 编辑 `bin/websocket-server.php`
2. 添加新的消息类型处理
3. 更新前端WebSocket客户端

## 🐛 故障排除

### 常见问题

**Q: WebSocket连接失败**
A: 检查端口8080是否被占用，确保防火墙允许连接

**Q: 视频流无法显示**
A: 检查摄像头配置，确认RTSP地址正确，或使用模拟视频流测试

**Q: 数据库连接错误**
A: 检查 `.env` 文件中的数据库配置，确保数据库服务正在运行

**Q: 页面样式异常**
A: 清除浏览器缓存，检查CSS文件是否正确加载

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**⚠️ 免责声明**: 本项目仅用于技术学习和演示目的，请遵守当地法律法规。
