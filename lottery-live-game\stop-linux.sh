#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}========================================"
echo -e "彩票直播游戏系统 - 停止服务器"
echo -e "========================================${NC}"

# 停止Laravel服务器
if [ -f "logs/laravel.pid" ]; then
    LARAVEL_PID=$(cat logs/laravel.pid)
    if kill -0 $LARAVEL_PID 2>/dev/null; then
        echo -e "${YELLOW}正在停止Laravel服务器 (PID: $LARAVEL_PID)...${NC}"
        kill $LARAVEL_PID
        sleep 2
        if kill -0 $LARAVEL_PID 2>/dev/null; then
            echo -e "${RED}强制停止Laravel服务器...${NC}"
            kill -9 $LARAVEL_PID
        fi
        echo -e "${GREEN}✓ Laravel服务器已停止${NC}"
    else
        echo -e "${YELLOW}Laravel服务器未运行${NC}"
    fi
    rm -f logs/laravel.pid
else
    echo -e "${YELLOW}未找到Laravel服务器PID文件${NC}"
fi

# 停止WebSocket服务器
if [ -f "logs/websocket.pid" ]; then
    WEBSOCKET_PID=$(cat logs/websocket.pid)
    if kill -0 $WEBSOCKET_PID 2>/dev/null; then
        echo -e "${YELLOW}正在停止WebSocket服务器 (PID: $WEBSOCKET_PID)...${NC}"
        kill $WEBSOCKET_PID
        sleep 2
        if kill -0 $WEBSOCKET_PID 2>/dev/null; then
            echo -e "${RED}强制停止WebSocket服务器...${NC}"
            kill -9 $WEBSOCKET_PID
        fi
        echo -e "${GREEN}✓ WebSocket服务器已停止${NC}"
    else
        echo -e "${YELLOW}WebSocket服务器未运行${NC}"
    fi
    rm -f logs/websocket.pid
else
    echo -e "${YELLOW}未找到WebSocket服务器PID文件${NC}"
fi

# 清理可能残留的进程
echo -e "\n${BLUE}清理残留进程...${NC}"
pkill -f "php artisan serve" 2>/dev/null
pkill -f "websocket-cross-platform.php" 2>/dev/null

echo -e "\n${GREEN}所有服务器已停止！${NC}"
