<?php

return [
    // 操作系统检测
    'is_windows' => strtoupper(substr(PHP_OS, 0, 3)) === 'WIN',
    'is_linux' => strtoupper(substr(PHP_OS, 0, 5)) === 'LINUX',
    'is_unix' => in_array(strtoupper(PHP_OS), ['LINUX', 'DARWIN', 'FREEBSD', 'OPENBSD']),
    
    // 服务器配置
    'servers' => [
        'laravel' => [
            'host' => env('APP_HOST', '127.0.0.1'),
            'port' => env('APP_PORT', 8000),
        ],
        'websocket' => [
            'host' => env('WEBSOCKET_HOST', '0.0.0.0'),
            'port' => env('WEBSOCKET_PORT', 8080),
        ],
        'http_api' => [
            'host' => env('HTTP_SERVER_HOST', '0.0.0.0'),
            'port' => env('HTTP_SERVER_PORT', 8081),
        ]
    ],
    
    // 进程配置
    'processes' => [
        'websocket_workers' => function() {
            return strtoupper(substr(PHP_OS, 0, 3)) === 'WIN' ? 1 : 4;
        },
        'max_connections' => env('MAX_CONNECTIONS', 1000),
    ],
    
    // 路径配置
    'paths' => [
        'logs' => storage_path('logs'),
        'pids' => storage_path('pids'),
        'uploads' => storage_path('app/uploads'),
        'cache' => storage_path('framework/cache'),
    ],
    
    // 数据库配置
    'database' => [
        'default_charset' => 'utf8mb4',
        'default_collation' => 'utf8mb4_unicode_ci',
        'strict_mode' => env('DB_STRICT_MODE', true),
    ],
    
    // 安全配置
    'security' => [
        'admin_path' => env('ADMIN_PATH', 'admin'),
        'csrf_protection' => true,
        'rate_limiting' => [
            'enabled' => true,
            'max_attempts' => 60,
            'decay_minutes' => 1,
        ],
    ],
    
    // 游戏配置
    'game' => [
        'round_duration' => env('GAME_ROUND_DURATION', 30),
        'auto_start' => env('GAME_AUTO_START', true),
        'default_user_balance' => env('DEFAULT_USER_BALANCE', 1000.00),
        'min_bet_amount' => env('MIN_BET_AMOUNT', 10),
        'max_bet_amount' => env('MAX_BET_AMOUNT', 10000),
    ],
    
    // 直播配置
    'streaming' => [
        'enabled' => env('STREAMING_ENABLED', true),
        'camera_host' => env('CAMERA_HOST', '*************'),
        'camera_username' => env('CAMERA_USERNAME', 'admin'),
        'camera_password' => env('CAMERA_PASSWORD', 'admin123'),
        'camera_port' => env('CAMERA_PORT', 554),
        'camera_path' => env('CAMERA_PATH', '/stream1'),
        'fallback_to_simulation' => env('STREAMING_FALLBACK', true),
        'frame_rate' => env('STREAMING_FPS', 30),
        'resolution' => env('STREAMING_RESOLUTION', '1280x720'),
    ],
    
    // 日志配置
    'logging' => [
        'channels' => [
            'websocket' => [
                'driver' => 'single',
                'path' => storage_path('logs/websocket.log'),
                'level' => env('LOG_LEVEL', 'debug'),
            ],
            'game' => [
                'driver' => 'single',
                'path' => storage_path('logs/game.log'),
                'level' => env('LOG_LEVEL', 'debug'),
            ],
            'streaming' => [
                'driver' => 'single',
                'path' => storage_path('logs/streaming.log'),
                'level' => env('LOG_LEVEL', 'debug'),
            ],
        ],
    ],
    
    // 性能配置
    'performance' => [
        'memory_limit' => env('MEMORY_LIMIT', '512M'),
        'max_execution_time' => env('MAX_EXECUTION_TIME', 0),
        'opcache_enabled' => function_exists('opcache_get_status'),
    ],
    
    // 监控配置
    'monitoring' => [
        'enabled' => env('MONITORING_ENABLED', true),
        'health_check_interval' => 30, // 秒
        'metrics_retention' => 7 * 24 * 60 * 60, // 7天
    ],
];
