<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>彩票直播游戏 - 手机版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 顶部状态栏 */
        .status-bar {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .username {
            color: #fff;
            font-weight: 600;
            font-size: 16px;
        }

        .balance {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(238, 90, 36, 0.3);
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 主容器 */
        .main-container {
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            height: calc(100vh - 60px);
        }

        /* 视频和游戏状态区域 */
        .video-section {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            aspect-ratio: 16/9;
            margin-bottom: 15px;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .video-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
            color: #ff6b6b;
            font-size: 14px;
            text-align: center;
        }

        .game-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 15px;
            margin-bottom: 15px;
        }

        .round-info {
            text-align: center;
        }

        .round-info h3 {
            font-size: 18px;
            margin-bottom: 5px;
            color: #fff;
        }

        .round-info p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .countdown {
            text-align: center;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            padding: 15px;
            border-radius: 50%;
            min-width: 80px;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(238, 90, 36, 0.4);
        }

        .countdown-label {
            font-size: 12px;
            margin-bottom: 5px;
        }

        .countdown-time {
            font-size: 24px;
            font-weight: bold;
        }

        /* 历史号码 */
        .history-numbers {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 15px;
            margin-bottom: 15px;
        }

        .history-title {
            font-size: 16px;
            margin-bottom: 10px;
            color: #fff;
            text-align: center;
        }

        .numbers-grid {
            display: grid;
            grid-template-columns: repeat(9, 1fr);
            gap: 8px;
        }

        .number-item {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            padding: 8px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
        }

        /* 投注区域 */
        .betting-section {
            flex: 1;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            overflow-y: auto;
        }

        .betting-title {
            font-size: 18px;
            margin-bottom: 20px;
            text-align: center;
            color: #fff;
        }

        /* 基础投注选项 */
        .basic-bets {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .bet-option {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 20px;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .bet-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .bet-option.active {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            box-shadow: 0 6px 20px rgba(238, 90, 36, 0.4);
        }

        .bet-option .odds {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
        }

        .bet-option .amount {
            position: absolute;
            top: 5px;
            right: 10px;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 12px;
            display: none;
        }

        .bet-option.has-bet .amount {
            display: block;
        }

        /* 范围投注 */
        .range-bets {
            margin-bottom: 25px;
        }

        .range-title {
            font-size: 16px;
            margin-bottom: 15px;
            color: #fff;
            text-align: center;
        }

        .range-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .range-option {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            color: #333;
            border: none;
            padding: 15px 10px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 3px 10px rgba(168, 237, 234, 0.3);
        }

        .range-option:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(168, 237, 234, 0.4);
        }

        .range-option.active {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: #333;
        }

        /* 数字投注 */
        .number-bets {
            margin-bottom: 25px;
        }

        .number-title {
            font-size: 16px;
            margin-bottom: 15px;
            color: #fff;
            text-align: center;
        }

        .number-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
        }

        .number-option {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            color: #333;
            border: none;
            padding: 12px 8px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 2px 8px rgba(255, 236, 210, 0.3);
        }

        .number-option:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 236, 210, 0.4);
        }

        .number-option.active {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: #333;
        }

        /* 投注控制 */
        .bet-controls {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }

        .amount-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .amount-label {
            font-size: 16px;
            color: #fff;
        }

        .amount-buttons {
            display: flex;
            gap: 10px;
        }

        .amount-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .amount-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .amount-btn.active {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            box-shadow: 0 4px 12px rgba(238, 90, 36, 0.4);
        }

        .confirm-bet {
            width: 100%;
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
        }

        .confirm-bet:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
        }

        .confirm-bet:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .main-container {
                padding: 10px;
            }
            
            .basic-bets {
                grid-template-columns: 1fr;
            }
            
            .range-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .number-grid {
                grid-template-columns: repeat(5, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- 顶部状态栏 -->
    <div class="status-bar">
        <div class="user-info">
            <span class="username">{{ Auth::user()->username }}</span>
            <span class="balance">积分: <span id="userBalance">{{ number_format(Auth::user()->balance, 2) }}</span></span>
        </div>
        <form method="POST" action="/logout" style="display: inline;">
            @csrf
            <button type="submit" class="logout-btn">退出</button>
        </form>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 视频和游戏状态区域 -->
        <div class="video-section">
            <!-- 视频容器 -->
            <div class="video-container">
                <div class="video-placeholder">
                    📹 摄像头未启用或不支持实时视频流
                </div>
            </div>

            <!-- 游戏状态 -->
            <div class="game-status">
                <div class="round-info">
                    <h3>第{{ $currentGame->version_number }}版</h3>
                    <p>第{{ $currentGame->round_number }}局</p>
                </div>
                <div class="countdown">
                    <div class="countdown-label">倒计时</div>
                    <div class="countdown-time" id="countdown">{{ $currentGame->remaining_time }}</div>
                </div>
                <div class="round-info">
                    <h3>在线</h3>
                    <p id="onlineUsers">{{ $onlineUsers }}人</p>
                </div>
            </div>

            <!-- 历史号码 -->
            <div class="history-numbers">
                <div class="history-title">📊 历史号码</div>
                <div class="numbers-grid" id="historyNumbers">
                    @if($historyGames->count() > 0)
                        @foreach($historyGames->take(1)->first()->result_numbers ?? [] as $number)
                            <div class="number-item">{{ $number }}</div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>

        <!-- 投注区域 -->
        <div class="betting-section">
            <div class="betting-title">🎯 投注选项</div>

            <!-- 基础投注选项 -->
            <div class="basic-bets">
                <button class="bet-option" data-type="small" data-value="">
                    <div>小 (1-12)</div>
                    <div class="odds">x2.0</div>
                    <div class="amount" id="amount-small">100</div>
                </button>
                <button class="bet-option" data-type="big" data-value="">
                    <div>大 (13-24)</div>
                    <div class="odds">x2.0</div>
                    <div class="amount" id="amount-big">100</div>
                </button>
                <button class="bet-option" data-type="odd" data-value="">
                    <div>单</div>
                    <div class="odds">x2.0</div>
                    <div class="amount" id="amount-odd">100</div>
                </button>
                <button class="bet-option" data-type="even" data-value="">
                    <div>双</div>
                    <div class="odds">x2.0</div>
                    <div class="amount" id="amount-even">100</div>
                </button>
            </div>

            <!-- 范围投注 -->
            <div class="range-bets">
                <div class="range-title">📈 范围投注</div>
                <div class="range-grid">
                    <button class="range-option" data-type="range" data-value="1-8">1-8 <small>x3</small></button>
                    <button class="range-option" data-type="range" data-value="9-16">9-16 <small>x3</small></button>
                    <button class="range-option" data-type="range" data-value="17-24">17-24 <small>x3</small></button>
                    <button class="range-option" data-type="range" data-value="1-6">1-6 <small>x4</small></button>
                    <button class="range-option" data-type="range" data-value="7-12">7-12 <small>x4</small></button>
                    <button class="range-option" data-type="range" data-value="13-18">13-18 <small>x4</small></button>
                    <button class="range-option" data-type="range" data-value="19-24">19-24 <small>x4</small></button>
                    <button class="range-option" data-type="range" data-value="1-3">1-3 <small>x8</small></button>
                    <button class="range-option" data-type="range" data-value="4-6">4-6 <small>x8</small></button>
                </div>
            </div>

            <!-- 数字投注 -->
            <div class="number-bets">
                <div class="number-title">🔢 数字投注</div>
                <div class="number-grid">
                    @for($i = 1; $i <= 24; $i++)
                        <button class="number-option" data-type="number" data-value="{{ $i }}">{{ $i }}</button>
                    @endfor
                </div>
            </div>

            <!-- 投注控制 -->
            <div class="bet-controls">
                <div class="amount-selector">
                    <span class="amount-label">投注金额:</span>
                    <div class="amount-buttons">
                        <button class="amount-btn active" data-amount="10">10</button>
                        <button class="amount-btn" data-amount="50">50</button>
                        <button class="amount-btn" data-amount="100">100</button>
                        <button class="amount-btn" data-amount="500">500</button>
                        <button class="amount-btn" data-amount="1000">1000</button>
                    </div>
                </div>
                <button class="confirm-bet" onclick="placeBet()">确认投注</button>
            </div>
        </div>
    </div>

    <script>
        let selectedBetType = null;
        let selectedBetValue = null;
        let selectedAmount = 10;
        let ws = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            initBettingInterface();
            initAmountSelector();
            updateGameStatus();
        });

        // WebSocket连接
        function initWebSocket() {
            try {
                ws = new WebSocket('ws://localhost:8080');
                
                ws.onopen = function() {
                    console.log('WebSocket连接成功');
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };
                
                ws.onclose = function() {
                    console.log('WebSocket连接关闭，尝试重连...');
                    setTimeout(initWebSocket, 3000);
                };
                
                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                };
            } catch (error) {
                console.error('WebSocket连接失败:', error);
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            if (data.type === 'game_status') {
                updateGameInfo(data.data);
            } else if (data.type === 'video_frame') {
                // 处理视频帧（如果需要）
            }
        }

        // 更新游戏信息
        function updateGameInfo(gameData) {
            if (gameData.round_number) {
                document.querySelector('.round-info h3').textContent = `第${gameData.version_number}版`;
                document.querySelector('.round-info p').textContent = `第${gameData.round_number}局`;
            }
            
            if (gameData.remaining_time !== undefined) {
                document.getElementById('countdown').textContent = Math.max(0, Math.floor(gameData.remaining_time));
            }
            
            if (gameData.online_users) {
                document.getElementById('onlineUsers').textContent = `${gameData.online_users}人`;
            }
            
            if (gameData.latest_results && gameData.latest_results.length > 0) {
                updateHistoryNumbers(gameData.latest_results);
            }
        }

        // 更新历史号码
        function updateHistoryNumbers(numbers) {
            const container = document.getElementById('historyNumbers');
            container.innerHTML = '';
            numbers.slice(0, 27).forEach(number => {
                const div = document.createElement('div');
                div.className = 'number-item';
                div.textContent = number;
                container.appendChild(div);
            });
        }

        // 投注界面初始化
        function initBettingInterface() {
            // 基础投注选项
            document.querySelectorAll('.bet-option').forEach(option => {
                option.addEventListener('click', function() {
                    selectedBetType = this.dataset.type;
                    selectedBetValue = this.dataset.value;
                    
                    // 移除其他选中状态
                    document.querySelectorAll('.bet-option, .range-option, .number-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    
                    // 添加选中状态
                    this.classList.add('active');
                });
            });

            // 范围投注选项
            document.querySelectorAll('.range-option').forEach(option => {
                option.addEventListener('click', function() {
                    selectedBetType = this.dataset.type;
                    selectedBetValue = this.dataset.value;
                    
                    // 移除其他选中状态
                    document.querySelectorAll('.bet-option, .range-option, .number-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    
                    // 添加选中状态
                    this.classList.add('active');
                });
            });

            // 数字投注选项
            document.querySelectorAll('.number-option').forEach(option => {
                option.addEventListener('click', function() {
                    selectedBetType = this.dataset.type;
                    selectedBetValue = this.dataset.value;
                    
                    // 移除其他选中状态
                    document.querySelectorAll('.bet-option, .range-option, .number-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    
                    // 添加选中状态
                    this.classList.add('active');
                });
            });
        }

        // 金额选择器初始化
        function initAmountSelector() {
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    selectedAmount = parseInt(this.dataset.amount);
                    
                    // 移除其他选中状态
                    document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));
                    
                    // 添加选中状态
                    this.classList.add('active');
                });
            });
        }

        // 下注
        async function placeBet() {
            if (!selectedBetType) {
                alert('请选择投注类型');
                return;
            }
            
            if (!selectedAmount || selectedAmount < 10) {
                alert('投注金额不能少于10');
                return;
            }
            
            try {
                const response = await fetch('/api/game/bet', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        bet_type: selectedBetType,
                        bet_value: selectedBetValue,
                        bet_amount: selectedAmount
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    // 更新余额
                    document.getElementById('userBalance').textContent = data.data.balance;
                    
                    // 显示投注金额
                    const amountElement = document.getElementById('amount-' + selectedBetType);
                    if (amountElement) {
                        amountElement.textContent = selectedAmount;
                        amountElement.parentElement.classList.add('has-bet');
                    }
                    
                    alert('下注成功！');
                } else {
                    alert(data.msg || '下注失败');
                }
            } catch (error) {
                alert('网络错误，请稍后重试');
                console.error('下注错误:', error);
            }
        }

        // 定期更新游戏状态
        function updateGameStatus() {
            fetch('/api/game/status')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        updateGameInfo(data.data);
                    }
                })
                .catch(error => {
                    console.error('获取游戏状态失败:', error);
                });
        }

        // 每5秒更新一次游戏状态
        setInterval(updateGameStatus, 5000);
    </script>
</body>
</html>
